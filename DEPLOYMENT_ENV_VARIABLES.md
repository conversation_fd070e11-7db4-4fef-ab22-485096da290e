# 🚀 CareAI Deployment Environment Variables

This file contains all the environment variables needed for deploying CareAI to production.

## 📁 Frontend Deployment (Render/Vercel/Netlify)

### For Render:
**Build Command:** `npm run build`
**Start Command:** `npm run preview:production`

### For Vercel/Netlify:
**Build Command:** `npm run build`
**Output Directory:** `dist`

Copy these exact values to your frontend deployment platform:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://gzeeaqiimsmpiocvnzuj.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.t2UPzEIeBJuj5ack5kOMVbzbr5L3p8Eu2BZtKepxVEI
VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6ZWVhcWlpbXNtcGlvY3ZuenVqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzEyNjgzMiwiZXhwIjoyMDU4NzAyODMyfQ.Nt-Ow-_Qg_E_JGm_SXhDBP-Nh_8QxJdgBkVwYhbAcXw

# Backend URLs (ACTUAL DEPLOYED URLs)
VITE_GEMINI_PROXY_URL=https://gemini-proxy-modifs.onrender.com
VITE_ML_BACKEND_URL=https://your-ml-backend.onrender.com

# Google Services
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBjzZHbqt0sY-_McuXh1DFEGI8rHUwibNo

# Email Configuration
VITE_EMAIL_USER=<EMAIL>
VITE_EMAIL_PASSWORD=cwct btnk icxv taza
VITE_EDGE_FUNCTION_URL=https://gzeeaqiimsmpiocvnzuj.supabase.co/functions/v1/send-email

# Firebase Configuration
VITE_FIREBASE_API_KEY=AIzaSyCpaH2_eU4sBYwoJU7dUUhaWLcoOQdfkz0
VITE_FIREBASE_AUTH_DOMAIN=careaiproto.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=careaiproto
VITE_FIREBASE_STORAGE_BUCKET=careaiproto.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=521435078556
VITE_FIREBASE_APP_ID=1:521435078556:web:5833977c31e66e4dcab259
VITE_FIREBASE_MEASUREMENT_ID=G-5DRH8ZLEXP

# API Keys
VITE_OPENFDA_API_KEY=eG7WIHusJI5OVDXetHgLLAa8VoboXzU0syw5KqXq
VITE_GEMINI_API_KEY=AIzaSyBIXbgZ3EE043v9RLa0Z_h93-BArAF-Hr4

# App Configuration
VITE_APP_NAME=CareAI
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=production
VITE_DISABLE_SW_IN_DEV=false
```

## 🔧 Gemini Proxy Backend Deployment (Render)

Copy these exact values to your Render service:

```env
# Server Configuration
NODE_ENV=production
PORT=3001

# Google Gemini API
GEMINI_API_KEY=AIzaSyBIXbgZ3EE043v9RLa0Z_h93-BArAF-Hr4

# Supabase Configuration
SUPABASE_URL=https://gzeeaqiimsmpiocvnzuj.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.t2UPzEIeBJuj5ack5kOMVbzbr5L3p8Eu2BZtKepxVEI
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.t2UPzEIeBJuj5ack5kOMVbzbr5L3p8Eu2BZtKepxVEI

# Pinecone Vector Database
PINECONE_API_KEY=pcsk_7Ja3Eb_RjiusV2FSEbXVwcvQh5EcyDor8acU2tsK3CiKnBcGD9eX8H8A7RH1ar7TmnfBvr
PINECONE_ENVIRONMENT=serverless
PINECONE_INDEX_NAME=medical-handbook
PINECONE_HOST=medical-handbook-1ni237d.svc.aped-4627-b74a.pinecone.io

# Optional Configuration
CORS_ORIGIN=https://your-frontend-domain.com
MAX_FILE_SIZE=10485760
UPLOAD_LIMIT=5
```

## 🤖 ML Backend Deployment (Render)

Copy these values to your ML backend service:

```env
# Server Configuration
PORT=8000
ENVIRONMENT=production

# Model Configuration
MODEL_PATH=/opt/render/project/src/backend/ml/models
ENABLE_GPU=false

# API Configuration
API_TITLE=CareAI ML API
API_VERSION=1.0.0

# CORS Configuration
ALLOWED_ORIGINS=https://your-frontend-domain.com

# Logging
LOG_LEVEL=INFO

# Hugging Face Token (ACTUAL TOKEN)
HF_TOKEN=*************************************

# Supabase Configuration
SUPABASE_URL=https://gzeeaqiimsmpiocvnzuj.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.t2UPzEIeBJuj5ack5kOMVbzbr5L3p8Eu2BZtKepxVEI
```

## 🔑 Additional Keys Needed

You still need to obtain these API keys:

1. **Hugging Face Token**: Get from https://huggingface.co/settings/tokens
2. **Pinecone Environment**: Get from your Pinecone dashboard
3. **News API Key**: Get from https://newsapi.org/ (optional)
4. **OpenAI API Key**: Get from https://platform.openai.com/api-keys (optional)

## 📂 Repository URLs

- **Frontend**: **************:AlphaFrederic94/Ukuqala.git
- **Gemini Proxy**: **************:AlphaFrederic94/gemini-proxy-modifs.git  
- **ML Backend**: **************:AlphaFrederic94/predictions-backend-service.git

## ✅ Status

- ✅ All major API keys found and configured
- ✅ Supabase fully configured
- ✅ Firebase fully configured  
- ✅ Google Maps API configured
- ✅ Gemini AI configured
- ✅ Email service configured
- ✅ OpenFDA API configured
- ✅ Pinecone API configured
- ⚠️ Need Hugging Face token for ML features
- ⚠️ Need Pinecone environment value
